const express = require('express');
const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const session = require('express-session');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));
app.use(session({
    secret: process.env.SESSION_SECRET || '63dream-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false } // Set to true in production with HTTPS
}));

// Data storage (in production, use a proper database)
const PROJECTS_FILE = 'data/projects.json';
const ADMIN_PASSWORD_HASH = process.env.ADMIN_PASSWORD_HASH || '$2b$10$default.hash.for.demo';

// Ensure data directory exists
if (!fs.existsSync('data')) {
    fs.mkdirSync('data');
}

// Initialize projects file if it doesn't exist
if (!fs.existsSync(PROJECTS_FILE)) {
    fs.writeFileSync(PROJECTS_FILE, JSON.stringify([], null, 2));
}

// Email configuration
const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Submit project endpoint
app.post('/api/submit-project', async (req, res) => {
    try {
        const { name, country, email, title, problem, solution, built, link } = req.body;
        
        // Validate required fields
        if (!name || !country || !email || !title || !problem || !solution || !built) {
            return res.status(400).json({ error: 'All required fields must be filled' });
        }
        
        // Create project object
        const project = {
            id: Date.now().toString(),
            name,
            country,
            email,
            title,
            problem,
            solution,
            built,
            link: link || '',
            status: 'pending',
            submittedAt: new Date().toISOString(),
            approvedAt: null
        };
        
        // Save to file
        const projects = JSON.parse(fs.readFileSync(PROJECTS_FILE, 'utf8'));
        projects.push(project);
        fs.writeFileSync(PROJECTS_FILE, JSON.stringify(projects, null, 2));
        
        // Send email notification (if configured)
        if (process.env.EMAIL_USER && process.env.EMAIL_PASS) {
            const mailOptions = {
                from: process.env.EMAIL_USER,
                to: process.env.ADMIN_EMAIL || process.env.EMAIL_USER,
                subject: `New Project Submission: ${title}`,
                html: `
                    <h2>New Project Submission</h2>
                    <p><strong>Project:</strong> ${title}</p>
                    <p><strong>Creator:</strong> ${name} (${country})</p>
                    <p><strong>Email:</strong> ${email}</p>
                    <p><strong>Problem:</strong> ${problem}</p>
                    <p><strong>Solution:</strong> ${solution}</p>
                    <p><strong>Built:</strong> ${built}</p>
                    ${link ? `<p><strong>Link:</strong> <a href="${link}">${link}</a></p>` : ''}
                    <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
                `
            };
            
            await transporter.sendMail(mailOptions);
        }
        
        res.json({ success: true, message: 'Project submitted successfully!' });
        
    } catch (error) {
        console.error('Error submitting project:', error);
        res.status(500).json({ error: 'Failed to submit project' });
    }
});

// Get approved projects
app.get('/api/projects', (req, res) => {
    try {
        const projects = JSON.parse(fs.readFileSync(PROJECTS_FILE, 'utf8'));
        const approvedProjects = projects.filter(p => p.status === 'approved');
        res.json(approvedProjects);
    } catch (error) {
        console.error('Error reading projects:', error);
        res.status(500).json({ error: 'Failed to load projects' });
    }
});

// Admin login
app.post('/api/admin/login', async (req, res) => {
    try {
        const { password } = req.body;
        
        if (!password) {
            return res.status(400).json({ error: 'Password required' });
        }
        
        const isValid = await bcrypt.compare(password, ADMIN_PASSWORD_HASH);
        
        if (isValid) {
            req.session.admin = true;
            res.json({ success: true });
        } else {
            res.status(401).json({ error: 'Invalid password' });
        }
        
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Admin logout
app.post('/api/admin/logout', (req, res) => {
    req.session.destroy();
    res.json({ success: true });
});

// Check admin status
app.get('/api/admin/status', (req, res) => {
    res.json({ isAdmin: !!req.session.admin });
});

// Get all projects (admin only)
app.get('/api/admin/projects', (req, res) => {
    if (!req.session.admin) {
        return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
        const projects = JSON.parse(fs.readFileSync(PROJECTS_FILE, 'utf8'));
        res.json(projects);
    } catch (error) {
        console.error('Error reading projects:', error);
        res.status(500).json({ error: 'Failed to load projects' });
    }
});

// Approve/reject project (admin only)
app.post('/api/admin/project/:id/:action', (req, res) => {
    if (!req.session.admin) {
        return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
        const { id, action } = req.params;
        
        if (!['approve', 'reject'].includes(action)) {
            return res.status(400).json({ error: 'Invalid action' });
        }
        
        const projects = JSON.parse(fs.readFileSync(PROJECTS_FILE, 'utf8'));
        const projectIndex = projects.findIndex(p => p.id === id);
        
        if (projectIndex === -1) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        if (action === 'approve') {
            projects[projectIndex].status = 'approved';
            projects[projectIndex].approvedAt = new Date().toISOString();
        } else {
            projects[projectIndex].status = 'rejected';
        }
        
        fs.writeFileSync(PROJECTS_FILE, JSON.stringify(projects, null, 2));
        
        res.json({ success: true });
        
    } catch (error) {
        console.error('Error updating project:', error);
        res.status(500).json({ error: 'Failed to update project' });
    }
});

// Admin panel route
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`63 DREAM server running on port ${PORT}`);
    console.log(`Admin panel: http://localhost:${PORT}/admin`);
});

module.exports = app; 