<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>63 DREAM - Admin Panel</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .inter { font-family: 'Inter', sans-serif; }
        .kenya-green { color: #4DB06A; }
        .kenya-orange { color: #F47C2B; }
        .bg-kenya-green { background-color: #4DB06A; }
        .bg-kenya-orange { background-color: #F47C2B; }
        .border-kenya-green { border-color: #4DB06A; }
        .border-kenya-orange { border-color: #F47C2B; }
        
        .btn-success { 
            background: linear-gradient(135deg, #4DB06A 0%, #45a05a 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover { 
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(77, 176, 106, 0.3);
        }
        
        .btn-danger { 
            background: linear-gradient(135deg, #F47C2B 0%, #e06a1a 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover { 
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(244, 124, 43, 0.3);
        }
        
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { 
            transform: translateY(-2px);
            border-color: #4DB06A;
            box-shadow: 0 8px 25px rgba(77, 176, 106, 0.15);
        }
    </style>
</head>
<body class="bg-black text-white inter min-h-screen">
    <!-- Header -->
    <header class="bg-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <img src="assets/one.png" alt="63 DREAM" class="h-10 w-10 mr-3">
                    <span class="text-2xl font-bold text-white tracking-widest" style="letter-spacing:0.1em;">63 <span class="kenya-green">DR</span><span class="kenya-orange">EAM</span></span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-400">Admin Panel</span>
                    <button onclick="logout()" class="text-gray-400 hover:text-white transition-colors">Logout</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Login Form -->
    <div id="login-section" class="min-h-screen flex items-center justify-center bg-black">
        <div class="max-w-md w-full mx-auto p-8">
            <div class="text-center mb-8">
                <img src="assets/one.png" alt="63 DREAM" class="h-16 w-16 mx-auto mb-4">
                <h1 class="text-3xl font-bold text-white mb-2">63 <span class="kenya-green">DR</span><span class="kenya-orange">EAM</span></h1>
                <p class="text-gray-400">Admin Access</p>
            </div>
            
            <form id="login-form" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <input type="password" id="password" required 
                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                </div>
                
                <button type="submit" class="w-full bg-kenya-green hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                    Login
                </button>
            </form>
            
            <div id="login-error" class="mt-4 text-center text-red-400 hidden"></div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin-dashboard" class="hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="text-2xl font-bold kenya-green" id="total-projects">0</div>
                    <div class="text-gray-400">Total Projects</div>
                </div>
                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="text-2xl font-bold kenya-orange" id="pending-projects">0</div>
                    <div class="text-gray-400">Pending Review</div>
                </div>
                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="text-2xl font-bold text-green-400" id="approved-projects">0</div>
                    <div class="text-gray-400">Approved</div>
                </div>
                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="text-2xl font-bold text-red-400" id="rejected-projects">0</div>
                    <div class="text-gray-400">Rejected</div>
                </div>
            </div>

            <!-- Filters -->
            <div class="flex flex-wrap gap-4 mb-8">
                <button onclick="filterProjects('all')" class="filter-btn active px-4 py-2 rounded-lg border border-gray-700 text-gray-300 hover:text-white transition-colors">
                    All Projects
                </button>
                <button onclick="filterProjects('pending')" class="filter-btn px-4 py-2 rounded-lg border border-gray-700 text-gray-300 hover:text-white transition-colors">
                    Pending
                </button>
                <button onclick="filterProjects('approved')" class="filter-btn px-4 py-2 rounded-lg border border-gray-700 text-gray-300 hover:text-white transition-colors">
                    Approved
                </button>
                <button onclick="filterProjects('rejected')" class="filter-btn px-4 py-2 rounded-lg border border-gray-700 text-gray-300 hover:text-white transition-colors">
                    Rejected
                </button>
            </div>

            <!-- Projects List -->
            <div id="projects-container" class="space-y-6">
                <!-- Projects will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Project Modal -->
    <div id="project-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="min-h-screen flex items-center justify-center p-4">
            <div class="bg-gray-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-6">
                        <h2 class="text-2xl font-bold text-white" id="modal-title">Project Details</h2>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
                    </div>
                    
                    <div id="modal-content" class="space-y-4">
                        <!-- Project details will be loaded here -->
                    </div>
                    
                    <div id="modal-actions" class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-700">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allProjects = [];
        let currentFilter = 'all';
        let currentProjectId = null;

        // Login functionality
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('login-error');
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ password })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('admin-dashboard').classList.remove('hidden');
                    loadProjects();
                } else {
                    errorDiv.textContent = result.error || 'Login failed';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Login error:', error);
                errorDiv.textContent = 'Login failed. Please try again.';
                errorDiv.classList.remove('hidden');
            }
        });

        // Logout functionality
        async function logout() {
            try {
                await fetch('/api/admin/logout', { method: 'POST' });
                document.getElementById('login-section').classList.remove('hidden');
                document.getElementById('admin-dashboard').classList.add('hidden');
                document.getElementById('password').value = '';
                document.getElementById('login-error').classList.add('hidden');
            } catch (error) {
                console.error('Logout error:', error);
            }
        }

        // Load projects
        async function loadProjects() {
            try {
                const response = await fetch('/api/admin/projects');
                allProjects = await response.json();
                updateStats();
                renderProjects();
            } catch (error) {
                console.error('Error loading projects:', error);
            }
        }

        // Update statistics
        function updateStats() {
            document.getElementById('total-projects').textContent = allProjects.length;
            document.getElementById('pending-projects').textContent = allProjects.filter(p => p.status === 'pending').length;
            document.getElementById('approved-projects').textContent = allProjects.filter(p => p.status === 'approved').length;
            document.getElementById('rejected-projects').textContent = allProjects.filter(p => p.status === 'rejected').length;
        }

        // Filter projects
        function filterProjects(status) {
            currentFilter = status;
            
            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-kenya-green', 'text-white');
                btn.classList.add('text-gray-300');
            });
            
            event.target.classList.add('active', 'bg-kenya-green', 'text-white');
            event.target.classList.remove('text-gray-300');
            
            renderProjects();
        }

        // Render projects
        function renderProjects() {
            const container = document.getElementById('projects-container');
            const filteredProjects = currentFilter === 'all' 
                ? allProjects 
                : allProjects.filter(p => p.status === currentFilter);
            
            if (filteredProjects.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">📋</div>
                        <h3 class="text-xl font-bold mb-2">No projects found</h3>
                        <p class="text-gray-400">No projects match the current filter.</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = filteredProjects.map(project => `
                <div class="card-hover bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">${project.title}</h3>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>${project.name} (${project.country})</span>
                                <span>${new Date(project.submittedAt).toLocaleDateString()}</span>
                                <span class="px-2 py-1 rounded text-xs ${getStatusClass(project.status)}">${project.status}</span>
                            </div>
                        </div>
                        <button onclick="viewProject('${project.id}')" class="text-kenya-green hover:text-white transition-colors">
                            View Details
                        </button>
                    </div>
                    <p class="text-gray-300 mb-4">${project.problem.substring(0, 150)}${project.problem.length > 150 ? '...' : ''}</p>
                    ${project.status === 'pending' ? `
                        <div class="flex space-x-4">
                            <button onclick="approveProject('${project.id}')" class="btn-success px-4 py-2 rounded text-sm font-semibold">
                                Approve
                            </button>
                            <button onclick="rejectProject('${project.id}')" class="btn-danger px-4 py-2 rounded text-sm font-semibold">
                                Reject
                            </button>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // Get status class
        function getStatusClass(status) {
            switch (status) {
                case 'pending': return 'bg-yellow-500 text-black';
                case 'approved': return 'bg-green-500 text-black';
                case 'rejected': return 'bg-red-500 text-white';
                default: return 'bg-gray-500 text-white';
            }
        }

        // View project details
        async function viewProject(projectId) {
            const project = allProjects.find(p => p.id === projectId);
            if (!project) return;
            
            currentProjectId = projectId;
            
            document.getElementById('modal-title').textContent = project.title;
            document.getElementById('modal-content').innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Submitted By</label>
                        <p class="text-white">${project.name} (${project.country})</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                        <p class="text-white">${project.email}</p>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Problem Being Solved</label>
                    <p class="text-white">${project.problem}</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">Solution Created</label>
                    <p class="text-white">${project.solution}</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1">What Has Been Built</label>
                    <p class="text-white">${project.built}</p>
                </div>
                
                ${project.link ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Project Link</label>
                        <a href="${project.link}" target="_blank" class="text-kenya-green hover:text-white">${project.link}</a>
                    </div>
                ` : ''}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Submitted</label>
                        <p class="text-white">${new Date(project.submittedAt).toLocaleString()}</p>
                    </div>
                    ${project.approvedAt ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">${project.status === 'approved' ? 'Approved' : 'Rejected'}</label>
                            <p class="text-white">${new Date(project.approvedAt).toLocaleString()}</p>
                        </div>
                    ` : ''}
                </div>
            `;
            
            if (project.status === 'pending') {
                document.getElementById('modal-actions').innerHTML = `
                    <button onclick="approveProject('${project.id}')" class="btn-success px-6 py-2 rounded text-sm font-semibold">
                        Approve Project
                    </button>
                    <button onclick="rejectProject('${project.id}')" class="btn-danger px-6 py-2 rounded text-sm font-semibold">
                        Reject Project
                    </button>
                `;
            } else {
                document.getElementById('modal-actions').innerHTML = `
                    <button onclick="closeModal()" class="px-6 py-2 rounded text-sm font-semibold bg-gray-600 hover:bg-gray-500 transition-colors">
                        Close
                    </button>
                `;
            }
            
            document.getElementById('project-modal').classList.remove('hidden');
        }

        // Close modal
        function closeModal() {
            document.getElementById('project-modal').classList.add('hidden');
            currentProjectId = null;
        }

        // Approve project
        async function approveProject(projectId) {
            try {
                const response = await fetch(`/api/admin/projects/${projectId}/approve`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await loadProjects();
                    if (currentProjectId === projectId) {
                        closeModal();
                    }
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                console.error('Error approving project:', error);
                alert('Error approving project. Please try again.');
            }
        }

        // Reject project
        async function rejectProject(projectId) {
            try {
                const response = await fetch(`/api/admin/projects/${projectId}/reject`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    await loadProjects();
                    if (currentProjectId === projectId) {
                        closeModal();
                    }
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                console.error('Error rejecting project:', error);
                alert('Error rejecting project. Please try again.');
            }
        }

        // Check if already logged in
        async function checkAuth() {
            try {
                const response = await fetch('/api/admin/check');
                const result = await response.json();
                
                if (result.authenticated) {
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('admin-dashboard').classList.remove('hidden');
                    loadProjects();
                }
            } catch (error) {
                console.error('Auth check error:', error);
            }
        }

        // Initialize
        checkAuth();
    </script>
</body>
</html> 