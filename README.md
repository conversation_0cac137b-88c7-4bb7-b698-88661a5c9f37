# 63 DREAM - We Are Our Builders

A revolutionary African platform empowering young Africans to build solutions for Africa. No waiting. No complaints. Just action.

## 🚀 Vision

63 DREAM is inspired by the spirit of 1963, when many African nations gained independence. The call now is clear: stop waiting, start building. This platform is not about complaining or reflecting on what was taken — it is about action. Young Africans must rise to solve their own problems, and this platform is the megaphone for that movement.

## ✨ Features

### For Users
- **Submit Projects**: Share your African solutions with the world
- **Browse Projects**: Discover innovative solutions built by Africans for Africa
- **Community**: Connect with other builders and innovators

### For Admins
- **Project Review**: Approve or reject submitted projects
- **Dashboard**: Monitor submissions and platform activity
- **Email Notifications**: Get notified of new submissions

## 🛠️ Tech Stack

- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Backend**: Node.js, Express.js
- **Email**: Nodemailer
- **Authentication**: bcrypt, express-session
- **Data Storage**: JSON files (can be upgraded to database)

## 🎨 Design

- **Theme**: Bold, revolutionary, African, unapologetic
- **Colors**: Kenyan flag colors (Black, <PERSON>, Green, White)
- **Typography**: Permanent Marker for headlines, Inter for body text
- **UX**: Professional, modern, mobile-responsive

## 📦 Installation

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd 63dream
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   # Server Configuration
   PORT=3000
   SESSION_SECRET=your-super-secret-session-key
   
   # Email Configuration (Optional)
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ADMIN_EMAIL=<EMAIL>
   
   # Admin Password (Generate with: node -e "console.log(require('bcrypt').hashSync('your-password', 10))")
   ADMIN_PASSWORD_HASH=your-hashed-password
   ```

4. **Add your logo**
   Place your logo file as `one.png` in the root directory

5. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the platform**
   - Main site: http://localhost:3000
   - Admin panel: http://localhost:3000/admin

## 🔐 Security Features

- **Form Validation**: Server-side validation for all submissions
- **XSS Protection**: Input sanitization
- **Session Management**: Secure admin sessions
- **Password Hashing**: bcrypt for admin passwords
- **HTTPS Ready**: Configured for production deployment

## 📧 Email Setup

To enable email notifications for new submissions:

1. **Gmail Setup** (Recommended for MVP):
   - Enable 2-factor authentication
   - Generate an App Password
   - Use the App Password in your `.env` file

2. **Other Email Providers**:
   - Update the transporter configuration in `server.js`
   - Modify SMTP settings as needed

## 🚀 Deployment

### Render (Recommended)
1. Connect your GitHub repository
2. Set environment variables in Render dashboard
3. Deploy automatically on push

### Vercel
1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel`
3. Set environment variables in Vercel dashboard

### Netlify
1. Build and deploy static files
2. Use Netlify Functions for API endpoints
3. Set environment variables in Netlify dashboard

## 📁 Project Structure

```
63dream/
├── index.html          # Main homepage
├── admin.html          # Admin panel
├── server.js           # Backend server
├── package.json        # Dependencies
├── README.md           # This file
├── one.png            # Logo/favicon
├── data/              # Data storage
│   └── projects.json  # Project submissions
└── .env               # Environment variables (create this)
```

## 🔧 Configuration

### Customizing Colors
The platform uses Kenyan flag colors. To customize:
- Edit the CSS variables in `index.html` and `admin.html`
- Look for `.kenya-red` and `.kenya-green` classes

### Adding Features
- **Database**: Replace JSON storage with MongoDB/PostgreSQL
- **File Uploads**: Add image upload functionality
- **User Accounts**: Implement user registration/login
- **Comments**: Add project discussion features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Inspired by the spirit of African independence (1963)
- Built for young African innovators
- Empowering the next generation of African builders

---

**We are our builders. We are our saviors. No one is coming to save us.**

*63 DREAM - Built by Africans. For Africans.* 