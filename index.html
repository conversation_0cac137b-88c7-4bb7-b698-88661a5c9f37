<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>63 DREAM - Rise & Build</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .permanent-marker { font-family: 'Permanent Marker', cursive; }
        .inter { font-family: 'Inter', sans-serif; }
        
        .hero-text { opacity: 0; transform: translateY(20px); transition: all 0.5s ease; }
        .hero-text.active { opacity: 1; transform: translateY(0); }
        
        .kenya-green { color: #4DB06A; }
        .kenya-orange { color: #F47C2B; }
        .bg-kenya-green { background-color: #4DB06A; }
        .bg-kenya-orange { background-color: #F47C2B; }
        .border-kenya-green { border-color: #4DB06A; }
        .border-kenya-orange { border-color: #F47C2B; }
        
        .btn-primary { 
            background: linear-gradient(135deg, #4DB06A 0%, #F47C2B 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(77, 176, 106, 0.3);
        }
        
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { 
            transform: translateY(-5px);
            border-color: #4DB06A;
            box-shadow: 0 10px 30px rgba(77, 176, 106, 0.2);
        }
        
        .africa-bg {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 600'%3E%3Cpath d='M 200,100 L 300,80 L 400,90 L 450,120 L 480,150 L 500,200 L 520,250 L 530,300 L 520,350 L 500,400 L 480,450 L 450,480 L 400,500 L 300,520 L 200,530 L 150,520 L 100,500 L 80,450 L 70,400 L 80,350 L 100,300 L 120,250 L 150,200 L 180,150 L 200,100 Z' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='2'/%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    </style>
</head>
<body class="bg-black text-white inter">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <img src="assets/one.png" alt="63 DREAM" class="h-10 w-10 mr-3">
                    <span class="text-2xl font-bold text-white tracking-widest" style="letter-spacing:0.1em;">63 <span class="kenya-green">DR</span><span class="kenya-orange">EAM</span></span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#about" class="text-gray-300 hover:text-white transition-colors">About</a>
                    <a href="#projects" class="text-gray-300 hover:text-white transition-colors">Projects</a>
                    <a href="#submit" class="text-gray-300 hover:text-white transition-colors">Submit</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center africa-bg">
        <div class="text-center z-10 px-4">
            <div id="hero-text-1" class="hero-text permanent-marker text-4xl md:text-6xl lg:text-7xl mb-4">
                <span class="kenya-green">Over 60%</span> of Africa's population is under 25
            </div>
            <div id="hero-subtext-1" class="hero-text text-lg md:text-xl text-gray-300 mb-8">
                The future is ours to build
            </div>
            
            <div id="hero-text-2" class="hero-text permanent-marker text-4xl md:text-6xl lg:text-7xl mb-4">
                <span class="kenya-orange">60%</span> of the world's uncultivated arable land
            </div>
            <div id="hero-subtext-2" class="hero-text text-lg md:text-xl text-gray-300 mb-8">
                The resources are here. The time is now.
            </div>
            
            <div id="hero-text-3" class="hero-text permanent-marker text-4xl md:text-6xl lg:text-7xl mb-4">
                <span class="kenya-green">90%</span> of the world's platinum
            </div>
            <div id="hero-subtext-3" class="hero-text text-lg md:text-xl text-gray-300 mb-8">
                We hold the keys to the future
            </div>
            
            <div id="hero-text-4" class="hero-text permanent-marker text-4xl md:text-6xl lg:text-7xl mb-4">
                <span class="kenya-orange">$2B</span> lost yearly to brain drain
            </div>
            <div id="hero-subtext-4" class="hero-text text-lg md:text-xl text-gray-300 mb-8">
                The power is in our hands now
            </div>
            
            <div id="hero-text-5" class="hero-text permanent-marker text-4xl md:text-6xl lg:text-7xl mb-4">
                We are our <span class="kenya-green">builders</span>. We are our <span class="kenya-orange">saviors</span>.
            </div>
            <div id="hero-subtext-5" class="hero-text text-lg md:text-xl text-gray-300 mb-8">
                No one is coming to save us
            </div>
            
            <div class="mt-12">
                <a href="#submit" class="btn-primary px-8 py-4 rounded-lg text-white font-bold text-lg inline-block">
                    It's our time. Show what you've built.
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-900">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h2 class="text-4xl md:text-5xl font-bold mb-8">
                What is <span class="kenya-green">63</span> <span class="kenya-orange">DREAM</span>?
            </h2>
            <p class="text-xl text-gray-300 leading-relaxed">
                63 DREAM is a wake-up call. A refusal to wait. A call for young Africans to rise, build, and solve problems. 
                Not tomorrow. <span class="kenya-green font-semibold">Now.</span> No complaints. No blame. Just bold action.
            </p>
            <p class="text-lg text-gray-400 mt-6">
                Africa doesn't need saving. It needs builders. Young Africans hold the key. 
                <span class="kenya-orange font-semibold">You are the 63 Dream.</span>
            </p>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-black">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-4xl md:text-5xl font-bold text-center mb-16">
                Featured <span class="kenya-green">Projects</span>
            </h2>
            
            <!-- Loading State -->
            <div id="projects-loading" class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-kenya-green"></div>
                <p class="mt-4 text-gray-400">Loading projects...</p>
            </div>
            
            <!-- Empty State -->
            <div id="projects-empty" class="text-center py-12 hidden">
                <div class="text-6xl mb-4">🚀</div>
                <h3 class="text-2xl font-bold mb-4">Be the First to Build</h3>
                <p class="text-gray-400 mb-8">No projects yet. Submit yours and inspire others to follow.</p>
                <a href="#submit" class="btn-primary px-6 py-3 rounded-lg text-white font-semibold">
                    Submit Your Project
                </a>
            </div>
            
            <!-- Projects Grid -->
            <div id="projects-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 hidden">
                <!-- Projects will be loaded here dynamically -->
            </div>
        </div>
    </section>

    <!-- Submission Section -->
    <section id="submit" class="py-20 bg-gray-900">
        <div class="max-w-4xl mx-auto px-4">
            <h2 class="text-4xl md:text-5xl font-bold text-center mb-16">
                Submit Your <span class="kenya-green">Project</span>
            </h2>
            
            <form id="project-form" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Your Name *</label>
                        <input type="text" name="name" required 
                               class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Country *</label>
                        <input type="text" name="country" required 
                               class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Email *</label>
                    <input type="email" name="email" required 
                           class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Project Title *</label>
                    <input type="text" name="title" required 
                           class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">What Problem Are You Solving? *</label>
                    <textarea name="problem" rows="3" required 
                              class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors resize-none"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">What Did You Create? *</label>
                    <textarea name="solution" rows="3" required 
                              class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors resize-none"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">What Have You Built So Far? *</label>
                    <textarea name="built" rows="3" required 
                              class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors resize-none"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Project Link (Optional)</label>
                    <input type="url" name="link" 
                           class="w-full px-4 py-3 bg-black border border-gray-700 rounded-lg text-white focus:border-kenya-green focus:outline-none transition-colors">
                </div>
                
                <div class="text-center pt-6">
                    <button type="submit" class="btn-primary px-12 py-4 rounded-lg text-white font-bold text-lg">
                        Submit Project
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-black border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <div class="flex items-center justify-center mb-6">
                <img src="assets/one.png" alt="63 DREAM" class="h-8 w-8 mr-3">
                <span class="text-xl font-bold text-white tracking-widest">63 <span class="kenya-green">DR</span><span class="kenya-orange">EAM</span></span>
            </div>
            <p class="text-gray-400">
                Built by Africans. For Africans. The revolution starts with us.
            </p>
        </div>
    </footer>

    <script>
        // Hero text animation
        const heroTexts = [
            { main: 'hero-text-1', sub: 'hero-subtext-1' },
            { main: 'hero-text-2', sub: 'hero-subtext-2' },
            { main: 'hero-text-3', sub: 'hero-subtext-3' },
            { main: 'hero-text-4', sub: 'hero-subtext-4' },
            { main: 'hero-text-5', sub: 'hero-subtext-5' }
        ];
        let currentIndex = 0;

        function showNextText() {
            heroTexts.forEach(item => {
                document.getElementById(item.main).classList.add('hidden');
                document.getElementById(item.sub).classList.add('hidden');
            });
            
            document.getElementById(heroTexts[currentIndex].main).classList.remove('hidden');
            document.getElementById(heroTexts[currentIndex].sub).classList.remove('hidden');
            
            setTimeout(() => {
                document.getElementById(heroTexts[currentIndex].main).classList.add('active');
                document.getElementById(heroTexts[currentIndex].sub).classList.add('active');
            }, 100);
            
            setTimeout(() => {
                document.getElementById(heroTexts[currentIndex].main).classList.remove('active');
                document.getElementById(heroTexts[currentIndex].sub).classList.remove('active');
            }, 3500);
            
            currentIndex = (currentIndex + 1) % heroTexts.length;
        }

        showNextText();
        setInterval(showNextText, 4000);

        // Load projects
        async function loadProjects() {
            const projectsGrid = document.getElementById('projects-grid');
            const loadingDiv = document.getElementById('projects-loading');
            const emptyDiv = document.getElementById('projects-empty');
            
            try {
                loadingDiv.classList.remove('hidden');
                projectsGrid.classList.add('hidden');
                emptyDiv.classList.add('hidden');
                
                const response = await fetch('/api/projects');
                const projects = await response.json();
                
                if (projects.length === 0) {
                    projectsGrid.classList.add('hidden');
                    loadingDiv.classList.add('hidden');
                    emptyDiv.classList.remove('hidden');
                    return;
                }
                
                projectsGrid.innerHTML = projects.map(project => `
                    <div class="card-hover bg-gray-800 p-6 rounded-lg border border-gray-700">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-white">${project.title}</h3>
                            <span class="px-3 py-1 bg-kenya-green text-black text-xs font-semibold rounded-full">${project.country}</span>
                        </div>
                        <p class="text-gray-300 mb-4">${project.problem.substring(0, 120)}${project.problem.length > 120 ? '...' : ''}</p>
                        <p class="text-gray-400 text-sm mb-4">${project.solution.substring(0, 100)}${project.solution.length > 100 ? '...' : ''}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">by ${project.name}</span>
                            ${project.link ? `<a href="${project.link}" target="_blank" class="text-kenya-orange hover:text-white text-sm font-semibold">View Project →</a>` : ''}
                        </div>
                    </div>
                `).join('');
                
                projectsGrid.classList.remove('hidden');
                loadingDiv.classList.add('hidden');
            } catch (error) {
                console.error('Error loading projects:', error);
                projectsGrid.classList.add('hidden');
                loadingDiv.classList.add('hidden');
                emptyDiv.classList.remove('hidden');
            }
        }

        // Form submission
        document.getElementById('project-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/api/submit-project', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('Project submitted successfully! We\'ll review it and get back to you soon.');
                    e.target.reset();
                } else {
                    alert('Error submitting project: ' + result.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error submitting project. Please try again.');
            }
        });

        // Load projects on page load
        loadProjects();
    </script>
</body>
</html> 