# 63 DREAM - Deployment Guide

This guide will help you deploy the 63 DREAM platform to various hosting services.

## 🚀 Quick Start

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Run setup script**
   ```bash
   npm run setup
   ```

3. **Test locally**
   ```bash
   npm start
   ```

4. **Deploy to your preferred platform**

## 📦 Deployment Options

### Render (Recommended for MVP)

Render is perfect for Node.js applications and offers a generous free tier.

1. **Create Render Account**
   - Go to [render.com](https://render.com)
   - Sign up with GitHub

2. **Connect Repository**
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Select the repository

3. **Configure Service**
   - **Name**: `63dream` (or your preferred name)
   - **Environment**: `Node`
   - **Build Command**: `npm install`
   - **Start Command**: `npm start`
   - **Plan**: Free (or paid for more resources)

4. **Set Environment Variables**
   In the Render dashboard, go to Environment → Environment Variables:
   ```
   PORT=10000
   SESSION_SECRET=your-super-secret-session-key
   ADMIN_PASSWORD_HASH=your-hashed-password
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ADMIN_EMAIL=<EMAIL>
   ```

5. **Deploy**
   - Click "Create Web Service"
   - Render will automatically deploy your app
   - Your site will be available at `https://your-app-name.onrender.com`

### Vercel

Vercel is great for static sites with serverless functions.

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

3. **Set Environment Variables**
   - Go to your Vercel dashboard
   - Navigate to Settings → Environment Variables
   - Add the same variables as above

### Netlify

Netlify works well for static sites with serverless functions.

1. **Build for Production**
   ```bash
   npm run build
   ```

2. **Deploy**
   - Drag and drop your `dist` folder to Netlify
   - Or connect your GitHub repository

3. **Configure Functions**
   - Set up Netlify Functions for API endpoints
   - Update environment variables in Netlify dashboard

### Railway

Railway is another excellent option for Node.js apps.

1. **Create Railway Account**
   - Go to [railway.app](https://railway.app)
   - Sign up with GitHub

2. **Deploy**
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository

3. **Configure**
   - Set environment variables in Railway dashboard
   - Railway will automatically detect Node.js and deploy

## 🔧 Environment Variables

Create a `.env` file in your production environment with:

```env
# Server Configuration
PORT=3000
SESSION_SECRET=your-super-secret-session-key

# Email Configuration (Optional)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>

# Admin Password
ADMIN_PASSWORD_HASH=your-hashed-password
```

### Generating Admin Password Hash

```bash
node -e "console.log(require('bcrypt').hashSync('your-password', 10))"
```

## 📧 Email Setup

### Gmail (Recommended)

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Enable 2FA

2. **Generate App Password**
   - Go to Security → App passwords
   - Generate a new app password
   - Use this password in your `EMAIL_PASS` variable

3. **Update Environment Variables**
   ```env
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-digit-app-password
   ```

### Other Email Providers

Update the transporter configuration in `server.js`:

```javascript
const transporter = nodemailer.createTransporter({
    host: 'your-smtp-host',
    port: 587,
    secure: false,
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    }
});
```

## 🔐 Security Checklist

- [ ] Change default session secret
- [ ] Use strong admin password
- [ ] Enable HTTPS (automatic on most platforms)
- [ ] Set up email notifications
- [ ] Configure proper CORS if needed
- [ ] Set up monitoring/logging

## 📊 Monitoring

### Basic Monitoring
- Check your hosting platform's built-in monitoring
- Set up uptime monitoring (UptimeRobot, Pingdom)

### Advanced Monitoring
- Add logging with Winston or similar
- Set up error tracking (Sentry)
- Monitor performance (New Relic, DataDog)

## 🔄 Continuous Deployment

### GitHub Actions (Optional)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm test
      # Add deployment steps for your platform
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Issues**
   - Most platforms set their own PORT
   - Use `process.env.PORT || 3000`

2. **File System Issues**
   - Some platforms have read-only file systems
   - Use environment variables or external databases

3. **Email Not Working**
   - Check SMTP settings
   - Verify app passwords
   - Test with a simple email client

4. **Admin Login Issues**
   - Regenerate password hash
   - Check session configuration
   - Verify environment variables

### Getting Help

- Check your hosting platform's logs
- Test locally first
- Verify all environment variables are set
- Check the platform's documentation

## 🎉 Success!

Once deployed, your 63 DREAM platform will be live and ready to empower young African innovators!

**Remember**: The revolution starts with us. No one will save us but us. ✊🏾 