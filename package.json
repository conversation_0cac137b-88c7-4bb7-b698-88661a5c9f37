{"name": "63dream", "version": "1.0.0", "description": "A revolutionary African platform empowering young Africans to build solutions for Africa", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["africa", "innovation", "platform", "projects", "empowerment"], "author": "63 DREAM Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "nodemailer": "^6.9.7", "bcrypt": "^5.1.1", "express-session": "^1.17.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}