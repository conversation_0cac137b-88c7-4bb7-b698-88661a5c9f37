#!/usr/bin/env node

const fs = require('fs');
const bcrypt = require('bcrypt');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🚀 63 DREAM Setup Script');
console.log('========================\n');

// Check if .env exists
if (fs.existsSync('.env')) {
    console.log('⚠️  .env file already exists. This will overwrite it.');
    rl.question('Continue? (y/N): ', (answer) => {
        if (answer.toLowerCase() !== 'y') {
            console.log('Setup cancelled.');
            rl.close();
            return;
        }
        runSetup();
    });
} else {
    runSetup();
}

function runSetup() {
    console.log('\n📝 Setting up 63 DREAM...\n');
    
    // Generate session secret
    const sessionSecret = require('crypto').randomBytes(32).toString('hex');
    
    rl.question('Enter admin password: ', (password) => {
        if (password.length < 6) {
            console.log('❌ Password must be at least 6 characters long.');
            rl.close();
            return;
        }
        
        // Hash the password
        const hashedPassword = bcrypt.hashSync(password, 10);
        
        // Create .env content
        const envContent = `# Server Configuration
PORT=3000
SESSION_SECRET=${sessionSecret}

# Email Configuration (Optional - for notifications)
EMAIL_USER=<EMAIL>
EMAIL_PASS=puga vqzw jzhu wyxb
ADMIN_EMAIL=<EMAIL>

# Admin Password (Generated from setup script)
ADMIN_PASSWORD_HASH=${hashedPassword}
`;
        
        // Write .env file
        fs.writeFileSync('.env', envContent);
        
        // Create data directory
        if (!fs.existsSync('data')) {
            fs.mkdirSync('data');
        }
        
        // Initialize projects.json
        if (!fs.existsSync('data/projects.json')) {
            fs.writeFileSync('data/projects.json', JSON.stringify([], null, 2));
        }
        
        console.log('\n✅ Setup complete!');
        console.log('\n📋 Next steps:');
        console.log('1. Install dependencies: npm install');
        console.log('2. Start the server: npm start');
        console.log('3. Visit: http://localhost:3000');
        console.log('4. Admin panel: http://localhost:3000/admin');
        console.log('\n🔐 Admin password: ' + password);
        console.log('\n📧 To enable email notifications, update the EMAIL_* variables in .env');
        
        rl.close();
    });
} 